 [TOKEN MANAGER] Token Manager inizializzato
 [STATE] Dati caricati da sessionStorage e rimossi
 [STATE] Controllo iniziale:
 [STATE] - skipAnimations: true
 [STATE] - fullInterface: true
 [STATE] - fromSessionStorage: true
 [STATE] - savedState: null
 [STATE] - shouldShowFullInterface: true
 [STATE] - hasActiveGame: false
 [STATE] - shouldDefaultToFullInterface: false
 [GAME] Dati da sessionStorage: attivo interfaccia completa
 [DRAG FIX] Removed setup-animation element from DOM
 [SOCKET INIT] Creating socket with token: Present
 [SOCKET HANDLERS] Handler socket aggiuntivi inizializzati
 [LOCAL GAME] Manager caricato e pronto
 [ONLINE GAME] Manager caricato e pronto
 [GAME MODE] Manager principale caricato e pronto
 [NAMES PROTECTION] Sistema di protezione nomi caricato
 [SMOOTH LOADING] Inizializzazione sistema loading fluido
 [SMOOTH LOADING] Sistema di loading fluido caricato
 [GAME] DOM caricato, mostro interfaccia completa
 [GAME] Game container mostrato con interfaccia completa
 [STATE] Stato salvato: Object
 [STATE] Dati da sessionStorage, URL già pulito
 [PRELOAD] Avvio precaricamento immagini delle carte: Array(4)
 [PLAYER NAMES] Modalità locale - Nome del giocatore 1 impostato a: bruscolino
 [INIT] Pagina di gioco rilevata, evito fullResetGameUI per prevenire refresh continui
 [PSN] Inizializzazione sistema PSN...
 [MULTIPLAYER] DOMContentLoaded - Inizio inizializzazione
 [MULTIPLAYER] URL: http://localhost:3000/game
 [CHAT] DEBUG: Chat event listener sarà registrato nell'evento connect quando socket è connesso
 [CHAT] DEBUG: Socket ID in initSocketEvents: undefined (potrebbe essere undefined)
 [CHAT] DEBUG: Socket presente ma potrebbe non essere ancora connesso. Socket ID: undefined
 [CHAT] Event listener per chatMessage sarà registrato quando il socket è connesso
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [ANIMATION] Osservatore impostato per rilevare il completamento dell'animazione
 [ONLINE ENHANCER] DOM pronto, inizializzo enhancer...
 victory-fix.js caricato.
 [PRELOAD] SUCCESSO precaricamento 1/4: http://localhost:3000/img/carte/card-back.webp
 [PRELOAD] SUCCESSO precaricamento 4/4: http://localhost:3000/img/Cover carte/cover.png
 [PRELOAD] SUCCESSO precaricamento 2/4: http://localhost:3000/img/carte/card-back.png
 [PRELOAD] SUCCESSO precaricamento 3/4: http://localhost:3000/img/Cover carte/cover.webp
 [SOCKET CONNECT] myPlayerId attuale: HVRZEmTrcp7ENuySAAAN socket.id: HVRZEmTrcp7ENuySAAAN
 [CHAT] DEBUG: Registrando event listener per chatMessage su socket connesso. Socket ID: HVRZEmTrcp7ENuySAAAN
 [CHAT] Event listener per chatMessage registrato su socket connesso
 [CHAT] DEBUG: Numero di listener per chatMessage: 1
 [BOARD CREATION] Creazione nuovo tabellone di gioco - Stack trace: Error
    at createGameBoard (http://localhost:3000/script.js:5245:87)
    at http://localhost:3000/js/game-state-manager.js:127:21
 [GAME] Tabellone di gioco creato
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [GAME] Slot mani giocatori creati
 [SMOOTH LOADING] Avvio animazioni di caricamento
 [SMOOTH LOADING] Animazioni di caricamento completate
 [GAME] Tab "Nuova Partita" attivato
 [GAME] Contenuto "Nuova Partita" mostrato
 [ONLINE ENHANCER] Finestra completamente caricata
 [ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili al caricamento
 [PERSISTENCE] Stato salvato scaduto o non presente
 [PERSISTENCE] Stato salvato rimosso
 [NAMES PROTECTION] Salvato nome player 1: Giocatore 1
 [NAMES PROTECTION] Salvato nome player 2: Giocatore 2
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: Giocatore 1
 [NAMES PROTECTION] Salvato nome player 2: Giocatore 2
 [GAME] Pulsante Nuova Partita cliccato
 [GAME] Modalità: online Tempo: none
 [MATCHMAKING] Avvio matchmaking online
 [MATCHMAKING] Socket già connesso
 [MATCHMAKING] Socket connesso, invio findMatch
 [MATCHMAKING] Invio findMatch con dati: Object
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SMOOTH LOADING] Gestendo transizione matchmaking
 [MATCHMAKING] Match trovato! Object
 [MATCHMAKING] Salvando dati partita con gameId: VFXUPG
 [STATE] Pulizia stato
 [MATCHMAKING] Stato interfaccia completa pulito
 [STATE] Partita iniziata, pulizia stato interfaccia completa
 [STATE] Dettagli partita: Object
 [STATE] Pulizia stato
 [MATCHMAKING] Match trovato - il flusso delle animazioni inizierà automaticamente senza refresh
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
 [PLAYER AREAS] Attivata animazione glow aggiungendo classe ready-for-play
 [GAME STATE] Prima chiamata a showGameSetup
 [GAME STATE] Partita multiplayer - preparazione setup e inizio animazioni
 [GAME SETUP] showGameSetup() chiamato
 [GAME SETUP] Modalità transizione fluida - evito reset visuale
 [GAME SETUP] Transizione fluida - skip visualizzazione container
 [GAME STATE] Partita online - procedendo con le animazioni...
 [GAME SETUP] showGameSetup() chiamato
 [GAME SETUP] Preparando game container per partita locale
 [GAME SETUP] Classe ready-for-play già presente, salto l'aggiunta
 [GAME SETUP] Tabellone esistente trovato, aggiorno rendering
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [GAME SETUP] Completato
 [DICE ANIMATION] animateDiceRoll chiamato - Animazione dei dadi commentata/disabilitata
 [DICE ANIMATION] Parametri: Object
 [DICE ANIM] Saltando animazione dadi, usando diceResult: Object e initialPosition: b6
 [DICE ANIM] Usando initialPosition (b6) -> Alpha: B, Numeric: 6
 [DICE ANIM] Dati dadi configurati:
 [DICE ANIM] Dado numerico mostrerà: 6
 [DICE ANIM] Dado alfabetico mostrerà: B
 [DICE ANIM] Dado colore mostrerà: black
 [DICE ANIM] initialPosition: b6
 [DICE ANIM] Saltata animazione dei dadi
 [DICE ANIM] Flag diceAnimationCompletedOnce impostato dopo salto animazione
 [ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer
 [ANIMATION] Flag globale gameContainerObserverTriggered impostato a true
 [ANIMATION] window.currentGameState presente, riapplico lo stato
 [ANIMATION] Observer disconnesso e rimosso
 [ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer
 [ANIMATION] Flag globale gameContainerObserverTriggered impostato a true
 [ANIMATION] window.currentGameState presente, riapplico lo stato
  [ERROR] Errore JavaScript rilevato: 
(anonymous) @ multiplayer.js:1780
  [ERROR] Messaggio: Uncaught TypeError: Cannot read properties of null (reading 'disconnect')
(anonymous) @ multiplayer.js:1781
  [ERROR] File: http://localhost:3000/js/multiplayer.js
(anonymous) @ multiplayer.js:1782
  [ERROR] Linea: 1942
(anonymous) @ multiplayer.js:1783
  [ERROR] Stack: TypeError: Cannot read properties of null (reading 'disconnect')
    at http://localhost:3000/js/multiplayer.js:1942:46
    at Array.forEach (<anonymous>)
    at MutationObserver.<anonymous> (http://localhost:3000/js/multiplayer.js:1918:19)
(anonymous) @ multiplayer.js:1784
multiplayer.js:1942  Uncaught 
 [ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l'animazione
 [ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l'animazione
 [SIDEBAR] Riabilitazione di tutti i tab
 [SIDEBAR] Tab riabilitato: gioca
 [SIDEBAR] Tab riabilitato: nuova-partita
 [SIDEBAR] Tab riabilitato: analisi
 [SIDEBAR] Tab riabilitato: giocatori
 [SIDEBAR] Passaggio al tab Gioca
 [SIDEBAR] Tab "Gioca" attivato
 [SIDEBAR] Contenuto "Gioca" mostrato
 [MATCHMAKING] Passaggio al tab Gioca completato
 [GAME STATE] Preparando transizione fluida da setup a game container
 [GAME STATE] Preparazione interfaccia multiplayer con pre-rendering
 [GAME STATE] Game container pre-renderizzato e pronto per fade-in
 [GAME STATE] Setup-animation non trovato
 [GAME STATE] Mostrando game container dopo preparazione
 [DICE ANIMATION] showGameContainer() chiamato
 [DICE ANIMATION] Nascondo dice animation area con fade-out
 [DICE ANIMATION] Nascondo dice animation overlay
 [SHOWGAME] Nomi già impostati definitivamente, preservo i valori attuali
 [SHOWGAME] Game container finale: Object
 [SHOWGAME DEBUG] Celle esistenti: 36 Dovrebbe creare tabellone: false
 [SHOWGAME] Tabellone già esistente, skip creazione
 [RESET UI] Tabellone già esistente con 36 celle, skip ricreazione
 [RESET UI] Reset memorizzazione permanente nomi e colori
 [RESET UI] Reset flag animazione dadi
 [RESET UI] Reset posizione iniziale originale
 [ONLINE ENHANCER] Evento gameContainerShown ricevuto
 [ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili
 [GAME STATE] Avvio animazioni carte dopo i dadi
 [NAMES PROTECTION] animateCardDealing intercettato
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [ANIM] Browser minimizzato durante animazione distribuzione carte
 [CARD DEALING] Deck area mostrata
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: 
 [NAMES PROTECTION] BLOCCATO svuotamento player2, mantengo: bruscolino
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
 [DECK IMAGE] Mazzo caricato con successo: http://localhost:3000/img/carte/card-back.webp
 [GAME STATE] Reset flag processingFirstOnlineState
 [DICE STATUS DEBUG] Tentativo strategia 2 - variabili globali
 [DICE STATUS DEBUG] Dopo strategia 2 - player1Name: ... player2Name: ...
 [DICE STATUS DEBUG] Tentativo strategia 3 - DOM fallback
 [DICE STATUS DEBUG] Trovato player1Name dal DOM: bruscolino
 [DICE STATUS DEBUG] Trovato player2Name dal DOM: bruscolino
 [DICE STATUS DEBUG] Dopo strategia 3 - player1Name: bruscolino player2Name: bruscolino
 [DICE STATUS] Aggiornato stato dadi: B6 - Bianco: bruscolino, Nero: bruscolino
 [ANIM] Avvio animazione carta iniziale: Object su b6
 [ANIM] Browser minimizzato, continuo l'animazione senza effetti particellari
 [ANIM] Inizio animazione movimento carta iniziale.
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [VISIBILITY] Trovate 11 cover cards bloccate, le rimuovo
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Animazione carta iniziale già in corso in background
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [ANIM] Fine animazione movimento. Rimuovo cover e mostro carta reale.
 [ANIM] Utilizzo fallback setTimeout per animazione finale
 [SOCKET] Cambio di visibilità del client owooW4SW2vSLHyQ8AAAP: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [VISIBILITY] Flag setup interrotto resettato
script.js:8712 [ANIM] Fine animateInitialCardPlacement con fallback.
script.js:11458 [ANIMATION DEBUG] Verifica condizioni per animazione nomi: Object
script.js:11486 [ANIMATION] Animando con G1 (Bianco): giggio vs G2 (Nero): bruscolino
script.js:11495 [ANIMATION] Convenzione standard: Bianco in alto, Nero in basso
script.js:8841 [PLAYER NAMES ANIMATION] ===== FUNZIONE CHIAMATA =====
script.js:8842 [PLAYER NAMES ANIMATION] Avvio animazione nomi giocatori DIRETTA
script.js:8843 [PLAYER NAMES ANIMATION] Nomi forniti: giggio vs bruscolino
script.js:8851 [PLAYER NAMES ANIMATION] Usando nomi forniti direttamente: giggio vs bruscolino
script.js:8886 [PLAYER NAMES ANIMATION] Nomi giocatori finali: giggio vs bruscolino
script.js:8905 [PLAYER NAMES ANIMATION] Game board trovato: true
script.js:8906 [PLAYER NAMES ANIMATION] Game board dimensioni: DOMRect
script.js:9416 [PLAYER NAMES ANIMATION] Animazione creata dinamicamente e aggiunta al DOM
script.js:9417 [PLAYER NAMES ANIMATION] Container aggiunto al game-board: dynamic-names-animation
script.js:9418 [PLAYER NAMES ANIMATION] Game-board children count: 41
script.js:9422 [PLAYER NAMES ANIMATION] Terminando animazione
script.js:9461 [PLAYER NAMES ANIMATION] Animazione completata e rimossa
script.js:9462 [PLAYER NAMES ANIMATION] Fade-in completato
script.js:9470 [PLAYER NAMES ANIMATION] isSetupAnimating resettato a false alla fine reale dell'animazione
script.js:9471 [PLAYER NAMES ANIMATION] nameAnimationCompleted impostato a true - animazione nomi completata
script.js:9474 [PLAYER NAMES ANIMATION] Forzando re-rendering delle carte dopo reset isSetupAnimating...
script.js:773 [SETUP RE-RENDER] Nessuno stato di gioco disponibile per re-rendering
script.js:11510 [ANIMATION] Animazioni completate e marcate per gameId: VFXUPG
script.js:11511 [ANIMATION] isSetupAnimating sarà resettato da animatePlayerNames() alla fine reale dell'animazione
script.js:11514 [ANIMATION] Chiamando handleGameState con animazioni completate
script.js:11919 [TURN PROTECTION] 🏁 Inizializzato wasInSetupPhase: false
script.js:11968 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
script.js:11969 [TURN PROTECTION] Turno era protetto: owooW4SW2vSLHyQ8AAAP
game-mode-manager.js:62 [GAME MODE] Manager inizializzato
game-mode-manager.js:104 [GAME MODE] Processando stato online senza manager attivo (normale durante inizializzazione)
script.js:12166 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12169 [GAME STATE] HVRZEmTrcp7ENuySAAAN (black): Array(5)
script.js:12169 [GAME STATE] owooW4SW2vSLHyQ8AAAP (white): Array(5)
script.js:12389 [PERMANENT NAMES] Nomi permanenti finali: {"HVRZEmTrcp7ENuySAAAN":"bruscolino","owooW4SW2vSLHyQ8AAAP":"giggio"}
script.js:12411 [PERMANENT COLORS] Colori permanenti finali: {"HVRZEmTrcp7ENuySAAAN":"black","owooW4SW2vSLHyQ8AAAP":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 5
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 5
script.js:12437 [EARLY PLAYER2 MONITOR] Observer installato il prima possibile, stato attuale: bruscolino
script.js:12444 [EARLY RESTORE] Controllo nomi: Object
script.js:12483 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12484 [ANIMATION DEBUG] - isStarting: false
script.js:12485 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12486 [ANIMATION DEBUG] - state.gameId: VFXUPG
script.js:12487 [ANIMATION DEBUG] - state.gameOver: false
script.js:12488 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12489 [ANIMATION DEBUG] - isFirstStateReceived: true
script.js:12490 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12491 [ANIMATION DEBUG] - state.mode: online
script.js:12492 [ANIMATION DEBUG] - window.animationsCompletedForGame: VFXUPG
script.js:12493 [ANIMATION DEBUG] - needsAnimations: true
script.js:12497 [ANIMATION DEBUG] ✅ AVVIO ANIMAZIONI - Condizioni soddisfatte!
script.js:12523 [STARTUP] Mantengo nomi permanenti esistenti dal matchFound
script.js:12594 [STARTUP] CORREZIONE IMMEDIATA (SERVER): Assegno nomi AUTORITATIVI - P1 (bianco): giggio , P2 (nero): bruscolino
player-names-protection.js:218 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
player-names-protection.js:239 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
script.js:12601 [STARTUP] Aggiornati dati permanenti con quelli autoritativi dal server
script.js:12625 [PLAYER2 MONITOR] Observer installato PRIMA delle operazioni per catturare tutte le modifiche
script.js:12628 [STARTUP] PRIMA di updatePlayerAreaNames - player1: giggio player2: bruscolino
script.js:12629 [STARTUP] DEBUG player-info-box - Verifica elementi DOM esistenti
script.js:12630 [STARTUP] player1NameElement: <span class=​"player-name loaded current-player" style>​giggio​</span>​
script.js:12631 [STARTUP] player2NameElement: <span class=​"player-name loaded">​bruscolino​</span>​
script.js:12634 [STARTUP] Controllo flag namesFinallySet: true
script.js:12636 [STARTUP] Nomi già definitivamente impostati, skip updatePlayerAreaNames
script.js:12643 [STARTUP] DOPO updatePlayerAreaNames - player1: giggio player2: bruscolino
script.js:12648 [DICE ANIMATION] Mostrando setup animazione dadi
script.js:12649 [DICE ANIMATION] isStarting: false
script.js:12650 [DICE ANIMATION] isOnlineMode: true
script.js:12651 [DICE ANIMATION] isFirstStateReceived: true
script.js:12655 [DICE ANIMATION] Prima chiamata a showGameSetup
script.js:3843 [GAME SETUP] showGameSetup() chiamato
script.js:3892 [GAME SETUP] Preparando game container per partita locale
script.js:3896 [GAME SETUP] Container già visibile, mantengo stato esistente
script.js:3953 [GAME SETUP] Classe ready-for-play già presente, salto l'aggiunta
script.js:3960 [GAME SETUP] Tabellone esistente trovato, aggiorno rendering
script.js:5310 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5323 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:3976 [GAME SETUP] Completato
script.js:12742 [INITIAL POSITION] Salvata posizione iniziale originale: b6
script.js:12822 [ANIMATION] Animazioni già completate per questo gioco, skip
script.js:11525 [PSN AUTHORIRATIVE] Registrazione mosse dal server prima di handleGameState
game-mode-manager.js:104 [GAME MODE] Processando stato online senza manager attivo (normale durante inizializzazione)
script.js:12166 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12169 [GAME STATE] HVRZEmTrcp7ENuySAAAN (black): Array(5)
script.js:12169 [GAME STATE] owooW4SW2vSLHyQ8AAAP (white): Array(5)
script.js:12389 [PERMANENT NAMES] Nomi permanenti finali: {"HVRZEmTrcp7ENuySAAAN":"bruscolino","owooW4SW2vSLHyQ8AAAP":"giggio"}
script.js:12411 [PERMANENT COLORS] Colori permanenti finali: {"HVRZEmTrcp7ENuySAAAN":"black","owooW4SW2vSLHyQ8AAAP":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 5
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 5
script.js:12483 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12484 [ANIMATION DEBUG] - isStarting: false
script.js:12485 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12486 [ANIMATION DEBUG] - state.gameId: VFXUPG
script.js:12487 [ANIMATION DEBUG] - state.gameOver: false
script.js:12488 [ANIMATION DEBUG] - isSetupAnimating: true
script.js:12489 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12490 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12491 [ANIMATION DEBUG] - state.mode: online
script.js:12492 [ANIMATION DEBUG] - window.animationsCompletedForGame: VFXUPG
script.js:12493 [ANIMATION DEBUG] - needsAnimations: false
script.js:14388 [UI UPDATE] Nome avversario da state.playerNames: giggio
script.js:14427 [UPDATE UI] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:14434 [UPDATE UI] Preservata posizione iniziale originale: b6
script.js:13621 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13687 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13688 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13744 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14453 [DEBUG] Vantaggio calcolato: 50%
script.js:15938 Tentativo di forzare rendering rating...
script.js:15965 Ratings dopo init: Object
script.js:15974 Aggiornamento avatar player1: owooW4SW2vSLHyQ8AAAP
script.js:15980 Aggiornamento avatar player2: HVRZEmTrcp7ENuySAAAN
script.js:14525 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:14528 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:14545 [UPDATE UI FINAL] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14667 [ONLINE UI] Player1/Player2 non mappati, rimappo manualmente da ID socket
script.js:14668 [ONLINE UI] state.players keys: Array(2)
script.js:14669 [ONLINE UI] permanentPlayerColors: Object
script.js:14692 [ONLINE UI] Mapping riuscito - P1 (bianco): 5 carte, P2 (nero): 5 carte
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 5
script.js:4806 [OPPONENT HAND] player1-hand: previousSize=null, currentSize=5, hasPlayedCard=false
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 5
script.js:4806 [OPPONENT HAND] player2-hand: previousSize=undefined, currentSize=5, hasPlayedCard=false
script.js:14917 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14918 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5310 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5323 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14920 [DEBUG] updateGameUI AFTER renderBoard
script.js:14921 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14922 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14942 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14943 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13119 [TURN TIMER] Observer già attivato, evito chiamata ricorsiva a updateGameStateWithTurnTimer
script.js:12427 [EARLY PLAYER2 MONITOR] textContent cambiato: bruscolino Causa: childList Valore precedente: null
script.js:12428 [EARLY PLAYER2 MONITOR] Stack trace della modifica
(anonymous) @ script.js:12428
(anonymous) @ script.js:12426
script.js:13062 [SETUP ANIMATION] Animazione terminata - drag and drop sarà abilitato da animatePlayerNames()
script.js:14379 [UI UPDATE] Nome avversario da variabile globale: giggio
script.js:14427 [UPDATE UI] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:14434 [UPDATE UI] Preservata posizione iniziale originale: b6
script.js:13621 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13687 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13688 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13744 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14453 [DEBUG] Vantaggio calcolato: 50%
script.js:15898 [HISTORY] Aggiunta mossa #1 alla cronologia
script.js:15938 Tentativo di forzare rendering rating...
script.js:15965 Ratings dopo init: Object
script.js:15974 Aggiornamento avatar player1: owooW4SW2vSLHyQ8AAAP
script.js:15980 Aggiornamento avatar player2: HVRZEmTrcp7ENuySAAAN
script.js:14525 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:14528 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:14545 [UPDATE UI FINAL] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14667 [ONLINE UI] Player1/Player2 non mappati, rimappo manualmente da ID socket
script.js:14668 [ONLINE UI] state.players keys: Array(2)
script.js:14669 [ONLINE UI] permanentPlayerColors: Object
script.js:14692 [ONLINE UI] Mapping riuscito - P1 (bianco): 5 carte, P2 (nero): 5 carte
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 5
script.js:4806 [OPPONENT HAND] player1-hand: previousSize=5, currentSize=5, hasPlayedCard=false
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 5
script.js:4806 [OPPONENT HAND] player2-hand: previousSize=5, currentSize=5, hasPlayedCard=false
script.js:14917 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14918 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5310 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5323 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14920 [DEBUG] updateGameUI AFTER renderBoard
script.js:14921 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14922 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14942 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14943 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13119 [TURN TIMER] Observer già attivato, evito chiamata ricorsiva a updateGameStateWithTurnTimer
script.js:2204 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2225 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2243 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2573 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2371 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2374 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2424 [VISIBILITY] NON riavvio animazione distribuzione carte, mostro stato attuale
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:2443 [VISIBILITY DEBUG] currentGameState presente: true
script.js:2444 [VISIBILITY DEBUG] renderGameState definito: true
script.js:2445 [VISIBILITY DEBUG] isMultiplayerGame: true
script.js:2447 [VISIBILITY DEBUG] currentGameState.mode: online
script.js:2454 [VISIBILITY] Partita multiplayer rilevata, skip renderGameState per evitare corruzione stato
[NEW] Explain Console errors by using Copilot in Edge: click
         
         to explain an error. 
        Learn more
        Don't show again
script.js:1166 [SOCKET] Ricevuto evento gameState: {gameId: 'VFXUPG', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
