/* Wrapper esterno per isolamento completo */
.skemino-page-wrapper {
    width: 100%;
    min-height: 100vh;
    height: auto;
    background: #000000;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    position: relative;
    scrollbar-gutter: stable;
    display: flex; /* Aggiunto per il contenimento corretto dei figli */
    flex-direction: column; /* Organizza il contenuto in colonna */
    z-index: 1;
}

/* Reset specifico solo per elementi Skèmino */
.skemino-app-container {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.skemino-app-container * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Stili base solo per il container Skèmino */
.skemino-app-container {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    position: relative;
    width: 100%;
    max-width: 1460px;
    margin: 0 auto;
    display: flex;
    background: #000000;
}

/* Background animato - specifico per Skèmino */
.skemino-app-container::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: 
        radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 44%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 45% 75%, rgba(255, 255, 255, 0.01) 0%, transparent 50%);
    z-index: 0;
    animation: skemino-backgroundMove 20s ease infinite;
    pointer-events: none;
}

@keyframes skemino-backgroundMove {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(-20px, -20px) rotate(1deg); }
    66% { transform: translate(20px, -10px) rotate(-1deg); }
}

/* Sidebar laterale fissa - sempre attaccata al bordo sinistro */
.skemino-sidebar {
    width: 260px;
    height: 100vh;
    background: linear-gradient(180deg, #1e1e2e 0%, #2d2d44 100%);
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Logo */
.skemino-logo_logo {
    padding: 30px 20px;
    text-align: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.skemino-logo_logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
    animation: skemino-pulse 4s ease-in-out infinite;
}

@keyframes skemino-pulse {
    0%, 100% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.2); opacity: 1; }
}

.skemino-logo_logo-image {
    width: 140px;
    height: auto;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    position: relative;
    z-index: 1;
}

.skemino-logo_logo-image:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

/* Navigazione */
.skemino-sidebar nav {
    flex: 1;
    padding: 30px 0;
}

.skemino-sidebar ul {
    list-style: none;
}

.skemino-sidebar li {
    padding: 18px 25px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    overflow: hidden;
}

.skemino-sidebar li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.2) 0%, transparent 100%);
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.skemino-sidebar li:hover {
    color: #fff;
    padding-left: 35px;
}

.skemino-sidebar li:hover::before {
    width: 100%;
}

.skemino-sidebar li.skemino-active {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-radius: 0 30px 30px 0;
    margin-right: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.skemino-sidebar li.skemino-active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 70%;
    background: #fff;
    border-radius: 2px;
    animation: skemino-activeBlink 2s ease-in-out infinite;
}

@keyframes skemino-activeBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.skemino-sidebar li i {
    width: 24px;
    text-align: center;
    font-size: 20px;
    transition: all 0.3s;
}

.skemino-sidebar li:hover i {
    transform: translateX(5px) scale(1.1);
}

/* Footer della sidebar */
.skemino-sidebar-footer {
    padding: 25px;
    border-top: 2px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.skemino-auth-btn {
    width: 100%;
    padding: 12px;
    margin-bottom: 12px;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.skemino-auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.skemino-auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.skemino-auth-btn:hover::before {
    left: 100%;
}

.skemino-user-info {
    color: #ecf0f1;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 500;
}

.skemino-sidebar-controls {
    margin-top: 20px;
}

.skemino-lang-switch, .skemino-support-btn {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.skemino-lang-switch:hover, .skemino-support-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Container principale con margine per la sidebar */
.skemino-main_wrapper {
    margin-left: 260px;
    width: calc(100% - 260px);
    backdrop-filter: blur(20px);
    position: relative;
    background: #000000;
}

.skemino-main-content {
    width: 100%;
}

.skemino-index-component {
    width: 960px;
    margin: 0 auto;
}

/* Hero Section adattata per sidebar */
.skm-hero-container {
    width: 960px;
    margin: 0 auto;
    padding: 0 0 60px 0;
    background: #ffffff;
    border-radius: 20px;
    position: relative;
    overflow: visible;
    margin-top: 40px;
    margin-bottom: 40px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.skm-hero-top-header {
    text-align: center;
    padding: 60px 40px;
    background: #4C55CB;
    position: relative;
    overflow: hidden;
    margin-bottom: 40px;
    border-radius: 20px 20px 0 0;
    color: white;
}

.skm-hero-top-header::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    top: -150px;
    left: -50px;
}

.skm-hero-top-header::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    bottom: -100px;
    right: 50px;
}

.skm-hero-top-header h2 {
    font-size: 42px;
    font-weight: 700;
    color: white;
    margin-bottom: 16px;
    letter-spacing: -1px;
    position: relative;
    z-index: 2;
}

.skm-hero-top-header p {
    font-size: 22px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
    position: relative;
    z-index: 2;
}

.skm-hero-flex-wrapper {
    display: flex;
    gap: 60px;
    padding: 0 40px;
    position: relative;
}

.skm-hero-container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    animation: skm-float-effect 6s ease-in-out infinite;
}

@keyframes skm-float-effect {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-30px) scale(1.1); }
}

/* Board Preview */
.skm-board-area {
    width: 400px;
    flex-shrink: 0;
    animation: skm-slide-left 1s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes skm-slide-left {
    from { 
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.skm-board-image-container {
    width: 400px;
    height: 400px;
    background: linear-gradient(45deg, #f0f0f0 0%, #ffffff 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.skm-board-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.skm-board-image-container:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(0, 0, 0, 0.1);
}

.skm-board-image-container:hover::after {
    transform: translateX(100%);
}

.skm-board-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.skm-board-image-container:hover .skm-board-img {
    transform: scale(1.1);
}

/* Hero Content */
.skm-hero-content-area {
    width: 460px;
    padding-left: 20px;
    animation: skm-slide-right 1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hero actions container */
.skemino-hero-actions-container {
    display: flex;
    gap: 40px;
    align-items: center;
}

/* Play button group */
.skm-play-button-group {
    width: 300px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-self: center;
}

@keyframes skm-slide-right {
    from { 
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Titoli hero */
.skm-hero-title-box h1 {
    font-size: 48px;
    font-weight: 800;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    line-height: 1.1;
    letter-spacing: -1px;
    animation: skm-fade-up 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

@keyframes skm-fade-up {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.skm-hero-title-box h2 {
    font-size: 24px;
    color: #666;
    margin-bottom: 48px;
    font-weight: 300;
    animation: skm-fade-up 1s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

/* User hero header per utenti loggati */
.skm-user-welcome-box {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 48px;
    animation: skm-fade-up 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
    position: relative;
    width: 100%;
}

.skm-user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    animation: skm-avatar-pulse 3s ease-in-out infinite;
    display: flex;
    align-items: center;
    justify-content: center;
}

.skm-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

@keyframes skm-avatar-pulse {
    0% { box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3); }
    50% { box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5); }
    100% { box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3); }
}

.skm-user-info h1 {
    font-size: 36px;
    margin-bottom: 8px;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.skm-user-info h2 {
    font-size: 20px;
    color: #666;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Flag icon styling */
#hero-flag {
    width: 24px;
    height: 18px;
    display: inline-block;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
}

.flag-icon {
    position: relative;
    display: inline-block;
    width: 1.33333333em;
    line-height: 1em;
}

.flag-icon:before {
    content: '\00a0';
}

.flag-icon-it {
    background-image: url('https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/it.svg');
}

.flag-icon-us {
    background-image: url('https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/us.svg');
}

.flag-icon-gb {
    background-image: url('https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/gb.svg');
}

.flag-icon-fr {
    background-image: url('https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/fr.svg');
}

.flag-icon-de {
    background-image: url('https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/de.svg');
}

.flag-icon-es {
    background-image: url('https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/es.svg');
}

/* Trophy icon for leaderboard */
.hero-leaderboard-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(45deg, #FFD700 0%, #FFA500 100%);
    color: white;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
    margin-left: 8px;
}

.hero-leaderboard-icon:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.5);
}

.hero-leaderboard-icon i {
    font-size: 16px;
}

/* Action icons section */
.skm-hero-action-icons {
    position: absolute;
    top: 20px;
    right: 40px;
    display: flex;
    gap: 15px;
    align-items: center;
    z-index: 10;
}

.hero-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(245, 245, 245, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.hero-action-icon:hover {
    transform: translateY(-2px) scale(1.1);
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 6px 15px rgba(102, 126, 234, 0.3);
}

.hero-action-icon i {
    font-size: 18px;
}

/* Message notification badge */
.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ff4444;
    color: white;
    font-size: 11px;
    font-weight: 600;
    min-width: 16px;
    height: 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Play Buttons */
.skm-play-button-group {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 360px;
}

.skm-game-button {
    height: 65px;
    width: 300px;
    font-size: 18px;
    border: none;
    border-radius: 20px;
    background: linear-gradient(45deg, #4CAF50 0%, #45a049 100%);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    padding: 0 20px;
    margin: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
    animation: skm-fade-up 1s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

.skm-game-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.skm-game-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 30px rgba(76, 175, 80, 0.4);
}

.skm-game-button:hover::before {
    left: 100%;
}

.skm-game-button i {
    font-size: 28px;
    transition: all 0.3s;
}

.skm-game-button:hover i {
    transform: rotate(360deg) scale(1.2);
}

.skm-button-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.skm-button-primary-text {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.skm-button-secondary-text {
    font-size: 14px;
    color: rgba(255,255,255,0.9);
    font-weight: 400;
    margin-top: 4px;
}

/* Stile specifico per i diversi pulsanti */
.skm-online-button {
    background: linear-gradient(45deg, #2196F3 0%, #1976D2 100%);
    box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
    animation-delay: 0.8s;
}

.skm-online-button:hover {
    box-shadow: 0 12px 30px rgba(33, 150, 243, 0.4);
}

/* Feature Boxes */
.skm-feature-container {
    display: flex;
    gap: 15px;
    flex-grow: 1;
    justify-content: space-between;
    max-width: 580px;
}

.skm-feature-box {
    width: 32%;
    min-width: 150px;
    max-width: 180px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
}

.skm-feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.skm-feature-image-box {
    height: 120px;
    overflow: hidden;
}

.skm-feature-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.5s;
}

.skm-feature-box:hover .skm-feature-image {
    transform: scale(1);
}

.skm-feature-text {
    padding: 10px 15px 35px 15px;
    min-height: 70px;
}

.skm-feature-text h3 {
    font-size: 15px;
    color: #333;
    margin-bottom: 3px;
    line-height: 1.3;
}

.skm-feature-text p {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 0;
}

.skm-soon-badge {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 4px 5px;
    font-size: 11px;
    font-weight: 600;
}

.skm-status-message {
    margin-top: 20px;
    font-size: 14px;
    color: #666;
    font-style: italic;
}

/* Rules Section */
.skemino-rules-section {
    width: 960px;
    margin: 0 auto;
    padding: 0 0 60px 0;
    background: #ffffff;
    position: relative;
    overflow: visible;
    margin-bottom: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.skemino-rules-header {
    text-align: center;
    padding: 60px 40px;
    background: #4CAF50;
    position: relative;
    overflow: hidden;
    margin-bottom: 40px;
    border-radius: 20px 20px 0 0;
    color: white;
}

.skemino-rules-header::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    top: -150px;
    left: -50px;
}

.skemino-rules-header::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    bottom: -100px;
    right: 50px;
}

.skemino-rules-title {
    font-size: 42px;
    font-weight: 700;
    color: white;
    margin-bottom: 16px;
    letter-spacing: -1px;
    position: relative;
    z-index: 2;
}

.skemino-rules-subtitle {
    font-size: 22px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
    position: relative;
    z-index: 2;
}

.skemino-section-header {
    text-align: center;
    margin-bottom: 80px;
    animation: skemino-fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.skemino-section-header h2 {
    font-size: 42px;
    font-weight: 800;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    letter-spacing: -1px;
}

.skemino-section-header p {
    font-size: 22px;
    color: #666;
    font-weight: 300;
}

.skemino-rules-content {
    display: flex;
    gap: 60px;
    align-items: flex-start;
    padding: 0 40px;
}

.skemino-rules-video {
    width: 480px;
    height: 270px;
    flex-shrink: 0;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.skemino-rules-video:hover {
    transform: scale(1.03);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.skemino-rules-video iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.skemino-rules-text {
    width: 400px;
}

.skemino-rules-text blockquote {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 30px;
    border-left: 5px solid;
    border-image: linear-gradient(180deg, #667eea 0%, #764ba2 100%) 1;
    margin-bottom: 30px;
    font-style: italic;
    color: #555;
    font-size: 16px;
    line-height: 1.8;
    border-radius: 0 15px 15px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.skemino-rules-text blockquote:hover {
    transform: translateX(5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.skemino-rules-text cite {
    display: block;
    text-align: right;
    margin-top: 15px;
    font-style: normal;
    color: #666;
    font-weight: 600;
}

.skemino-rules-text p {
    font-size: 17px;
    line-height: 1.8;
    color: #333;
    margin-bottom: 30px;
}

.skemino-learn-more-btn {
    width: 200px;
    height: 50px;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.skemino-learn-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.skemino-learn-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.skemino-learn-more-btn:hover::before {
    left: 100%;
}

/* Academy Section */
.skemino-academy-section {
    width: 960px;
    margin: 0 auto;
    padding: 0 0 60px 0;
    background: #ffffff;
    position: relative;
    margin-bottom: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    overflow: visible;
}

.skemino-academy-header {
    text-align: center;
    padding: 60px 40px;
    background: #764ba2;
    position: relative;
    overflow: hidden;
    margin-bottom: 40px;
    border-radius: 20px 20px 0 0;
    color: white;
}

.skemino-academy-header::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    top: -150px;
    left: -50px;
}

.skemino-academy-header::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    bottom: -100px;
    right: 50px;
}

.skemino-academy-title {
    font-size: 42px;
    font-weight: 700;
    color: white;
    margin-bottom: 16px;
    letter-spacing: -1px;
    position: relative;
    z-index: 2;
}

.skemino-academy-subtitle {
    font-size: 22px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
    position: relative;
    z-index: 2;
}

.skemino-features-section {
    display: grid;
    grid-template-columns: repeat(3, 280px);
    gap: 40px;
    justify-content: center;
}

.skemino-feature-card {
    width: 280px;
    height: 320px;
    background: #fff;
    border-radius: 20px;
    padding: 40px 32px;
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
    animation: skemino-fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.skemino-feature-card:nth-child(2) {
    animation-delay: 0.4s;
}

.skemino-feature-card:nth-child(3) {
    animation-delay: 0.6s;
}

.skemino-feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.skemino-feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.skemino-feature-card:hover::before {
    transform: translateY(0);
}

.skemino-feature-card i {
    font-size: 56px;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 24px;
    transition: all 0.4s;
    position: relative;
    z-index: 1;
}

.skemino-feature-card:hover i {
    transform: scale(1.1) rotate(5deg);
}

.skemino-feature-card h3 {
    font-size: 22px;
    margin-bottom: 16px;
    color: #333;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.skemino-feature-card p {
    font-size: 15px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
}

.skemino-read-more {
    font-size: 15px;
    color: #667eea;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    z-index: 1;
    transition: all 0.3s;
}

.skemino-feature-card:hover .skemino-read-more {
    color: #764ba2;
    gap: 12px;
}

/* Animazione di caricamento iniziale */
.skemino-sidebar,
.skemino-main-wrapper {
    animation: skemino-initialLoad 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes skemino-initialLoad {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar personalizzata - applicata specificamente a .skemino-page-wrapper */
.skemino-page-wrapper::-webkit-scrollbar {
    width: 14px !important;
    position: fixed !important;
    right: 0 !important;
}

.skemino-page-wrapper::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.8) !important;
    border-radius: 0 !important;
}

.skemino-page-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 7px !important;
    border: 2px solid rgba(0, 0, 0, 0.8) !important;
    min-height: 50px !important;
}

.skemino-page-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #764ba2 0%, #667eea 100%) !important;
}

/* Firefox */
.skemino-page-wrapper {
    scrollbar-width: thin !important;
    scrollbar-color: #667eea rgba(0, 0, 0, 0.8) !important;
}

/* Assicuriamoci che il body e html non mostrino scrollbar */
html {
    overflow: hidden !important; /* Impedisce scrollbar in html */
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

body {
    overflow: hidden !important; /* Impedisce scrollbar in body */
    min-height: 100% !important;
    height: 100% !important; /* Fissa altezza al 100% */
    margin: 0 !important;
    background: #000000 !important;
    position: fixed !important; /* Fissa la posizione */
    width: 100% !important; /* Occupa tutta la larghezza */
    top: 0 !important;
    left: 0 !important;
    visibility: visible !important; /* Forza la visibilità per homelogged */
}

/* Forza il game-container a essere nascosto in homelogged */
body:has(.skemino-main_wrapper) #game-container {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Assicura che homepage sia sempre visibile in homelogged */
body:has(.skemino-main_wrapper) #homepage {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Stili unificati per l'elemento principale */

/* Animazione per la scrollbar */
@keyframes scrollPulse {
    0% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
}

/* Quando l'utente scrolla, anima la scrollbar */
.skemino-page-wrapper:hover::-webkit-scrollbar-thumb {
    animation: scrollPulse 2s infinite;
}

/* Daily Matches Section */
.daily-matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.daily-match-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.daily-match-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.daily-match-card.vittoria {
    border-color: #4CAF50;
}

.daily-match-card.sconfitta {
    border-color: #f44336;
}

.match-header {
    background: rgba(0, 0, 0, 0.02);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.match-body {
    padding: 20px;
}

.match-players {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.player-name {
    color: #333;
}

.vs {
    color: #999;
    font-weight: 400;
    font-size: 14px;
}

.opponent-name {
    color: #666;
}

.match-score {
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.match-result {
    text-align: center;
    padding: 8px 15px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
}

.match-result.vittoria {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.match-result.sconfitta {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.match-result i {
    margin-right: 5px;
}

/* Tabs */
.match-tabs-container {
    padding: 0 40px;
}

.match-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.tab-button {
    background: none;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-button i {
    font-size: 18px;
}

.tab-button.active {
    color: #667eea;
}

.tab-button::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-button.active::after {
    transform: scaleX(1);
}

.tab-button:hover {
    color: #667eea;
}

.tab-content {
    animation: fadeIn 0.5s ease-in-out;
}

/* Match History Section */
.match-history-container {
    padding: 20px 40px;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.history-table thead {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.history-table th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.history-table td {
    padding: 15px;
    font-size: 15px;
    color: #333;
}

.history-row.vittoria {
    background: rgba(76, 175, 80, 0.02);
}

.history-row.sconfitta {
    background: rgba(244, 67, 54, 0.02);
}

.opponent-name {
    font-weight: 600;
    color: #555;
}

.result-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
}

.result-badge.vittoria {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.result-badge.sconfitta {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.match-score {
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

/* Stats Section */
.stats-container {
    padding: 20px 40px;
}

.stats-two-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.stats-left-column,
.stats-right-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stats-mini-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-card {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.stat-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-value {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #000;
}

.stat-label {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #333;
    opacity: 0.8;
}

.stat-card.primary .stat-value,
.stat-card.primary .stat-label {
    color: white;
}

.stat-details {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-box {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.stat-box h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stat-box h4 i {
    color: #667eea;
}

.stat-box p {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
}

.favorite-cards {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.favorite-cards h4 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.favorite-cards h4 i {
    color: #e91e63;
}

.cards-list {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.fav-card {
    text-align: center;
}

.fav-card img {
    width: 80px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.fav-card:hover img {
    transform: scale(1.1);
}

.fav-card span {
    display: block;
    margin-top: 8px;
    font-weight: 600;
    color: #666;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive per statistiche su schermi medi */
@media screen and (max-width: 1024px) {
    .stats-two-column-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .stats-mini-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 480px) {
    .stats-mini-grid {
        grid-template-columns: 1fr;
    }
}

/* Media query solo per dispositivi mobili */
@media only screen and (max-device-width: 768px) {
    .skemino-sidebar {
        width: 100%;
        height: auto;
        position: static;
    }

    .skemino-main-wrapper {
        margin-left: 0;
        width: 100%;
        min-width: auto;
    }

    .skemino-index-component,
    .skm-hero-container,
    .skemino-rules-section,
    .skemino-academy-section {
        width: 100%;
        padding: 60px 20px;
    }

    .skm-hero-container {
        flex-direction: column;
    }

    .skm-board-area,
    .skm-board-image-container {
        width: 100%;
        max-width: 400px;
        height: 400px;
        margin: 0 auto;
    }

    .skm-hero-content-area {
        width: 100%;
        padding-left: 0;
        padding-top: 40px;
        align-items: center;
    }

    .skm-play-button-group {
        width: 100%;
        max-width: 360px;
    }

    .skm-game-button {
        width: 100%;
    }

    .skemino-rules-content {
        flex-direction: column;
    }

    .skemino-rules-video,
    .skemino-rules-text {
        width: 100%;
    }

    .skemino-features-section {
        grid-template-columns: 1fr;
    }

    .skemino-feature-card {
        width: 100%;
    }
}

